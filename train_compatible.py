"""
兼容版本训练脚本
专门针对PyTorch 2.0.1版本优化
"""
import os
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm
from pathlib import Path
import glob
import argparse
import gc

from dataset.dezh import DeZhTranslationDataset
from model.distillation_transformer import TeacherTransformerModel, StudentTransformerModel, DistillationLoss
from config import Config, Config4GB, ConfigQuick
from utils import collate_fn, clear_memory, print_training_info, save_model_safely, load_model_safely


def train_teacher_compatible(config_class=Config):
    """兼容版本的教师模型训练"""
    config = config_class()
    device = config.get_device()
    
    print("=" * 60)
    print("教师模型训练 - 兼容版本")
    print("=" * 60)
    config.print_config()
    
    # 创建目录
    from config import PathConfig
    PathConfig.create_directories()
    
    # 检查压缩数据集
    compressed_data_dir = "data/de-zh1_compressed"
    train_files = sorted(glob.glob(f"{compressed_data_dir}/train/*.txt"))

    if not train_files:
        print(f"错误：在 {compressed_data_dir}/train/ 中未找到训练文件")
        print("请先运行数据压缩：python compress_dataset.py")
        return None
    
    print(f"找到 {len(train_files)} 个训练文件")
    
    # 获取词汇表（从第一个文件）
    temp_dataset = DeZhTranslationDataset(train_files[0])
    de_vocab = temp_dataset.de_vocab
    zh_vocab = temp_dataset.zh_vocab
    del temp_dataset
    clear_memory()
    
    # 创建教师模型
    teacher_model = TeacherTransformerModel(
        d_model=config.TEACHER_DIM,
        src_vocab=de_vocab,
        tgt_vocab=zh_vocab,
        max_seq_length=config.MAX_SEQ_LENGTH,
        device=device,
        num_layers=config.TEACHER_LAYERS,
        num_heads=config.TEACHER_HEADS
    ).to(device)
    
    # 打印训练信息
    total_samples = sum(len(open(f, 'r', encoding='utf-8').readlines()) for f in train_files)
    print_training_info("教师", teacher_model, total_samples, config.BATCH_SIZE, config.TEACHER_EPOCHS)
    
    # 训练设置
    criterion = nn.CrossEntropyLoss(ignore_index=2)
    optimizer = torch.optim.AdamW(teacher_model.parameters(), lr=config.LEARNING_RATE, weight_decay=0.01)
    writer = SummaryWriter(config.TEACHER_OUTPUT_DIR + "/logs")
    
    # 兼容的混合精度设置
    scaler = torch.cuda.amp.GradScaler() if config.USE_AMP else None
    
    best_loss = float('inf')
    global_step = 0
    
    for epoch in range(config.TEACHER_EPOCHS):
        print(f"\n开始第 {epoch+1}/{config.TEACHER_EPOCHS} 轮训练")
        teacher_model.train()
        epoch_total_loss = 0
        
        for file_idx, train_file in enumerate(train_files):
            print(f"处理文件 {file_idx+1}/{len(train_files)}: {Path(train_file).name}")
            
            # 加载当前文件的数据
            file_dataset = DeZhTranslationDataset(train_file)
            file_loader = DataLoader(
                file_dataset, 
                batch_size=config.BATCH_SIZE, 
                shuffle=True, 
                collate_fn=lambda batch: collate_fn(batch, config.MAX_SEQ_LENGTH, device)
            )
            
            file_loss = 0
            loop = tqdm(file_loader, desc=f"Epoch {epoch+1} File {file_idx+1}/{len(train_files)}")
            
            for batch_idx, (src, tgt) in enumerate(loop):
                # 梯度累积
                if batch_idx % config.GRADIENT_ACCUMULATION_STEPS == 0:
                    optimizer.zero_grad()
                
                # 准备输入和目标
                tgt_input = tgt[:, :-1]
                tgt_output = tgt[:, 1:]
                
                # 前向传播（兼容版本）
                if config.USE_AMP and scaler is not None:
                    with torch.cuda.amp.autocast():
                        _, logits = teacher_model(src, tgt_input)
                        loss = criterion(logits.reshape(-1, logits.size(-1)), tgt_output.reshape(-1))
                        loss = loss / config.GRADIENT_ACCUMULATION_STEPS
                else:
                    _, logits = teacher_model(src, tgt_input)
                    loss = criterion(logits.reshape(-1, logits.size(-1)), tgt_output.reshape(-1))
                    loss = loss / config.GRADIENT_ACCUMULATION_STEPS
                
                # 反向传播
                if config.USE_AMP and scaler is not None:
                    scaler.scale(loss).backward()
                else:
                    loss.backward()
                
                # 梯度累积后更新
                if (batch_idx + 1) % config.GRADIENT_ACCUMULATION_STEPS == 0:
                    if config.USE_AMP and scaler is not None:
                        scaler.unscale_(optimizer)
                        torch.nn.utils.clip_grad_norm_(teacher_model.parameters(), max_norm=1.0)
                        scaler.step(optimizer)
                        scaler.update()
                    else:
                        torch.nn.utils.clip_grad_norm_(teacher_model.parameters(), max_norm=1.0)
                        optimizer.step()
                
                file_loss += loss.item() * config.GRADIENT_ACCUMULATION_STEPS
                
                # 记录日志
                if global_step % config.LOG_EVERY == 0:
                    writer.add_scalar('Loss/Train', loss.item() * config.GRADIENT_ACCUMULATION_STEPS, global_step)
                
                loop.set_postfix({'loss': loss.item() * config.GRADIENT_ACCUMULATION_STEPS})
                global_step += 1
                
                # 定期清理内存
                if batch_idx % 100 == 0:
                    clear_memory()
            
            epoch_total_loss += file_loss / len(file_loader)
            
            # 清理当前文件的数据
            del file_dataset, file_loader
            clear_memory()
        
        avg_loss = epoch_total_loss / len(train_files)
        print(f"教师模型 Epoch {epoch+1} 平均损失: {avg_loss:.4f}")
        
        # 保存最佳模型
        if avg_loss < best_loss:
            model_path = Path(config.TEACHER_OUTPUT_DIR) / "checkpoints" / "teacher_best.pt"
            if save_model_safely(teacher_model, model_path):
                best_loss = avg_loss
                print(f"新的最佳教师模型已保存，损失: {best_loss:.4f}")
        
        # 定期保存检查点
        if (epoch + 1) % config.SAVE_EVERY == 0:
            checkpoint_path = Path(config.TEACHER_OUTPUT_DIR) / "checkpoints" / f"teacher_epoch_{epoch+1}.pt"
            save_model_safely(teacher_model, checkpoint_path)
            print(f"检查点已保存: teacher_epoch_{epoch+1}.pt")
    
    writer.close()
    print("\n教师模型训练完成！")
    print(f"最佳模型: {config.TEACHER_OUTPUT_DIR}/checkpoints/teacher_best.pt")
    
    return teacher_model


def train_student_compatible(teacher_model_path=None, config_class=Config):
    """兼容版本的学生模型训练"""
    config = config_class()
    device = config.get_device()
    
    print("=" * 60)
    print("学生模型训练 - 兼容版本")
    print("=" * 60)
    config.print_config()
    
    # 创建目录
    from config import PathConfig
    PathConfig.create_directories()
    
    # 加载教师模型
    if teacher_model_path is None:
        teacher_model_path = PathConfig.TEACHER_BEST_MODEL
    
    if not Path(teacher_model_path).exists():
        print(f"错误：找不到教师模型 {teacher_model_path}")
        print("请先训练教师模型")
        return None
    
    print(f"加载教师模型: {teacher_model_path}")
    teacher_model = load_model_safely(teacher_model_path, device)
    if teacher_model is None:
        return None
    
    teacher_model.eval()
    
    # 使用压缩数据集进行学生模型训练
    compressed_data_dir = "data/de-zh1_compressed"
    train_files = sorted(glob.glob(f"{compressed_data_dir}/train/*.txt"))[:4]  # 使用前4个压缩文件

    if not train_files:
        print(f"错误：在 {compressed_data_dir}/train/ 中未找到训练文件")
        print("请先运行数据压缩：python compress_dataset.py")
        return None

    print(f"使用 {len(train_files)} 个压缩文件进行学生模型训练")
    
    # 合并选定的文件数据
    all_lines = []
    for file_path in train_files:
        with open(file_path, 'r', encoding='utf-8') as f:
            all_lines.extend(f.readlines())
    
    # 创建临时数据文件
    temp_data_file = "temp_student_data.txt"
    with open(temp_data_file, 'w', encoding='utf-8') as f:
        f.writelines(all_lines)
    
    # 加载数据
    dataset = DeZhTranslationDataset(temp_data_file)
    train_loader = DataLoader(
        dataset, 
        batch_size=config.BATCH_SIZE, 
        shuffle=True, 
        collate_fn=lambda batch: collate_fn(batch, config.MAX_SEQ_LENGTH, device)
    )
    
    # 创建学生模型
    student_model = StudentTransformerModel(
        d_model=config.STUDENT_DIM,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=config.MAX_SEQ_LENGTH,
        device=device,
        num_layers=config.STUDENT_LAYERS,
        num_heads=config.STUDENT_HEADS
    ).to(device)
    
    # 打印模型信息
    from utils import count_parameters
    teacher_params = count_parameters(teacher_model)
    student_params = count_parameters(student_model)
    compression_ratio = teacher_params / student_params
    
    print(f"\n模型对比:")
    print(f"  教师模型参数: {teacher_params:,}")
    print(f"  学生模型参数: {student_params:,}")
    print(f"  参数压缩比: {compression_ratio:.2f}x")
    
    print_training_info("学生", student_model, len(dataset), config.BATCH_SIZE, config.STUDENT_EPOCHS)
    
    # 训练设置
    criterion = DistillationLoss(alpha=config.ALPHA, temperature=config.TEMPERATURE)
    optimizer = torch.optim.AdamW(student_model.parameters(), lr=config.LEARNING_RATE, weight_decay=0.01)
    writer = SummaryWriter(config.STUDENT_OUTPUT_DIR + "/logs")
    
    # 兼容的混合精度设置
    scaler = torch.cuda.amp.GradScaler() if config.USE_AMP else None
    
    best_loss = float('inf')
    global_step = 0
    
    for epoch in range(config.STUDENT_EPOCHS):
        print(f"\n开始第 {epoch+1}/{config.STUDENT_EPOCHS} 轮训练")
        student_model.train()
        epoch_total_loss = 0
        
        loop = tqdm(train_loader, desc=f"Student Epoch {epoch+1}/{config.STUDENT_EPOCHS}")
        
        for batch_idx, (src, tgt) in enumerate(loop):
            # 梯度累积
            if batch_idx % config.GRADIENT_ACCUMULATION_STEPS == 0:
                optimizer.zero_grad()
            
            # 准备输入和目标
            tgt_input = tgt[:, :-1]
            tgt_output = tgt[:, 1:]
            
            # 教师模型前向传播（不计算梯度）
            with torch.no_grad():
                _, teacher_logits = teacher_model(src, tgt_input)
            
            # 学生模型前向传播（兼容版本）
            if config.USE_AMP and scaler is not None:
                with torch.cuda.amp.autocast():
                    _, student_logits = student_model(src, tgt_input)
                    
                    # 计算蒸馏损失
                    hard_loss, soft_loss, total_loss_batch = criterion(
                        student_logits.reshape(-1, student_logits.size(-1)),
                        teacher_logits.reshape(-1, teacher_logits.size(-1)),
                        tgt_output.reshape(-1)
                    )
                    total_loss_batch = total_loss_batch / config.GRADIENT_ACCUMULATION_STEPS
            else:
                _, student_logits = student_model(src, tgt_input)
                
                # 计算蒸馏损失
                hard_loss, soft_loss, total_loss_batch = criterion(
                    student_logits.reshape(-1, student_logits.size(-1)),
                    teacher_logits.reshape(-1, teacher_logits.size(-1)),
                    tgt_output.reshape(-1)
                )
                total_loss_batch = total_loss_batch / config.GRADIENT_ACCUMULATION_STEPS
            
            # 反向传播
            if config.USE_AMP and scaler is not None:
                scaler.scale(total_loss_batch).backward()
            else:
                total_loss_batch.backward()
            
            # 梯度累积后更新
            if (batch_idx + 1) % config.GRADIENT_ACCUMULATION_STEPS == 0:
                if config.USE_AMP and scaler is not None:
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(student_model.parameters(), max_norm=1.0)
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    torch.nn.utils.clip_grad_norm_(student_model.parameters(), max_norm=1.0)
                    optimizer.step()
            
            epoch_total_loss += total_loss_batch.item() * config.GRADIENT_ACCUMULATION_STEPS
            
            # 记录日志
            if global_step % config.LOG_EVERY == 0:
                writer.add_scalar('Loss/Total', total_loss_batch.item() * config.GRADIENT_ACCUMULATION_STEPS, global_step)
                writer.add_scalar('Loss/Hard', hard_loss.item(), global_step)
                writer.add_scalar('Loss/Soft', soft_loss.item(), global_step)
            
            loop.set_postfix({
                'total': total_loss_batch.item() * config.GRADIENT_ACCUMULATION_STEPS,
                'hard': hard_loss.item(),
                'soft': soft_loss.item()
            })
            global_step += 1
            
            # 定期清理内存
            if batch_idx % 100 == 0:
                clear_memory()
        
        avg_loss = epoch_total_loss / len(train_loader)
        print(f"学生模型 Epoch {epoch+1} 总损失: {avg_loss:.4f}")
        
        # 保存最佳模型
        if avg_loss < best_loss:
            model_path = Path(config.STUDENT_OUTPUT_DIR) / "checkpoints" / "student_best.pt"
            if save_model_safely(student_model, model_path):
                best_loss = avg_loss
                print(f"新的最佳学生模型已保存，损失: {best_loss:.4f}")
        
        # 定期保存检查点
        if (epoch + 1) % config.SAVE_EVERY == 0:
            checkpoint_path = Path(config.STUDENT_OUTPUT_DIR) / "checkpoints" / f"student_epoch_{epoch+1}.pt"
            save_model_safely(student_model, checkpoint_path)
            print(f"检查点已保存: student_epoch_{epoch+1}.pt")
    
    writer.close()
    
    # 清理临时文件
    if os.path.exists(temp_data_file):
        os.remove(temp_data_file)
    
    print("\n学生模型训练完成！")
    print(f"最佳模型: {config.STUDENT_OUTPUT_DIR}/checkpoints/student_best.pt")
    
    return student_model


def main():
    parser = argparse.ArgumentParser(description='兼容版本训练脚本')
    parser.add_argument('--mode', choices=['teacher', 'student', 'both'], default='both',
                       help='训练模式')
    parser.add_argument('--config', choices=['default', '4gb', 'quick'], default='4gb',
                       help='配置类型')
    parser.add_argument('--teacher_model', help='教师模型路径（学生训练时使用）')
    parser.add_argument('--clean_data', action='store_true', default=True,
                       help='是否使用数据清洗')
    
    args = parser.parse_args()
    
    # 选择配置
    config_classes = {
        'default': Config,
        '4gb': Config4GB,
        'quick': ConfigQuick
    }
    config_class = config_classes[args.config]
    
    print("🚀 兼容版本训练脚本")
    print(f"训练模式: {args.mode}")
    print(f"配置类型: {args.config}")
    
    if args.mode in ['teacher', 'both']:
        print("\n开始训练教师模型...")
        teacher_model = train_teacher_compatible(config_class)
        if teacher_model is None:
            print("❌ 教师模型训练失败")
            return
        print("✅ 教师模型训练完成")
    
    if args.mode in ['student', 'both']:
        print("\n开始训练学生模型...")
        student_model = train_student_compatible(args.teacher_model, config_class)
        if student_model is None:
            print("❌ 学生模型训练失败")
            return
        print("✅ 学生模型训练完成")
    
    print("\n🎉 训练完成！")


if __name__ == "__main__":
    main()
