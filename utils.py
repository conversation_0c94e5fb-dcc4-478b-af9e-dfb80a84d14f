"""
工具函数模块
包含数据处理、内存管理、训练辅助函数
"""
import os
import torch
import gc
import random
from pathlib import Path
from torch.nn.functional import pad


def collate_fn(batch, max_seq_length, device):
    """数据批处理函数"""
    de_batch, zh_batch = zip(*batch)
    
    # 填充到相同长度
    de_padded = pad_sequence(de_batch, max_seq_length, 2)  # 2是<pad>的索引
    zh_padded = pad_sequence(zh_batch, max_seq_length, 2)
    
    return de_padded.to(device), zh_padded.to(device)


def pad_sequence(sequences, max_length, pad_value):
    """将序列填充到指定长度"""
    padded_sequences = []
    for seq in sequences:
        if len(seq) > max_length:
            padded_seq = seq[:max_length]
        else:
            padding_length = max_length - len(seq)
            padded_seq = pad(seq, (0, padding_length), value=pad_value)
        padded_sequences.append(padded_seq)
    return torch.stack(padded_sequences)


def clear_memory():
    """清理GPU内存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()


def check_gpu_memory():
    """检查GPU内存"""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"GPU内存: {gpu_memory:.1f} GB")
        return gpu_memory
    else:
        print("未检测到CUDA GPU")
        return 0


def split_dataset(input_file, output_dir, split_size=30000, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1):
    """
    分割数据集为训练、验证和测试集
    """
    print(f"开始分割数据集: {input_file}")
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    train_dir = output_path / "train"
    val_dir = output_path / "val"
    test_dir = output_path / "test"
    
    train_dir.mkdir(exist_ok=True)
    val_dir.mkdir(exist_ok=True)
    test_dir.mkdir(exist_ok=True)
    
    # 读取所有数据
    print("读取数据文件...")
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    total_lines = len(lines)
    print(f"总数据量: {total_lines:,} 条")
    
    # 随机打乱数据
    random.seed(42)  # 确保可重现性
    random.shuffle(lines)
    
    # 计算分割点
    train_end = int(total_lines * train_ratio)
    val_end = train_end + int(total_lines * val_ratio)
    
    train_lines = lines[:train_end]
    val_lines = lines[train_end:val_end]
    test_lines = lines[val_end:]
    
    print(f"训练集: {len(train_lines):,} 条")
    print(f"验证集: {len(val_lines):,} 条")
    print(f"测试集: {len(test_lines):,} 条")
    
    # 分割训练集
    print("分割训练集...")
    split_and_save(train_lines, train_dir, "train", split_size)
    
    # 分割验证集
    print("分割验证集...")
    split_and_save(val_lines, val_dir, "val", split_size)
    
    # 分割测试集
    print("分割测试集...")
    split_and_save(test_lines, test_dir, "test", split_size)
    
    # 创建数据集信息文件
    info_file = output_path / "dataset_info.txt"
    with open(info_file, 'w', encoding='utf-8') as f:
        f.write(f"原始数据集: {input_file}\n")
        f.write(f"总数据量: {total_lines:,}\n")
        f.write(f"分割大小: {split_size:,}\n")
        f.write(f"训练集: {len(train_lines):,} 条\n")
        f.write(f"验证集: {len(val_lines):,} 条\n")
        f.write(f"测试集: {len(test_lines):,} 条\n")
        f.write(f"训练集文件数: {len(list(train_dir.glob('*.txt')))}\n")
        f.write(f"验证集文件数: {len(list(val_dir.glob('*.txt')))}\n")
        f.write(f"测试集文件数: {len(list(test_dir.glob('*.txt')))}\n")
    
    print(f"数据集分割完成！输出目录: {output_dir}")
    print(f"数据集信息已保存到: {info_file}")


def split_and_save(lines, output_dir, prefix, split_size):
    """将数据行分割并保存到多个文件"""
    for i in range(0, len(lines), split_size):
        file_num = i // split_size + 1
        chunk = lines[i:i + split_size]
        
        output_file = output_dir / f"{prefix}_{file_num:03d}.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(chunk)
        
        print(f"  保存 {output_file.name}: {len(chunk):,} 条数据")


def save_model_safely(model, path):
    """安全保存模型"""
    try:
        torch.save(model, path)
        return True
    except Exception as e:
        print(f"保存模型失败: {e}")
        return False


def load_model_safely(path, device):
    """安全加载模型"""
    try:
        model = torch.load(path, map_location=device, weights_only=False)
        return model
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None


def count_parameters(model):
    """计算模型参数数量"""
    return sum(p.numel() for p in model.parameters())


def format_time(seconds):
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        return f"{seconds/60:.1f}分钟"
    else:
        return f"{seconds/3600:.1f}小时"


def print_training_info(model_type, model, dataset_size, batch_size, epochs):
    """打印训练信息"""
    print(f"\n{model_type}模型训练信息:")
    print(f"  模型参数数量: {count_parameters(model):,}")
    print(f"  数据集大小: {dataset_size:,}")
    print(f"  批次大小: {batch_size}")
    print(f"  训练轮次: {epochs}")
    print(f"  每轮批次数: {dataset_size // batch_size}")
    print(f"  总训练步数: {(dataset_size // batch_size) * epochs:,}")


def create_optimizer(model, lr=0.0001, weight_decay=0.01):
    """创建优化器"""
    return torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)


def create_scaler(use_amp=True):
    """创建混合精度缩放器"""
    if use_amp:
        try:
            # 尝试新版本API
            return torch.amp.GradScaler('cuda')
        except AttributeError:
            # 回退到旧版本API
            return torch.cuda.amp.GradScaler()
    return None


def validate_paths(*paths):
    """验证路径是否存在"""
    for path in paths:
        if not Path(path).exists():
            print(f"路径不存在: {path}")
            return False
    return True


def get_file_size(file_path):
    """获取文件大小（MB）"""
    if Path(file_path).exists():
        size_bytes = Path(file_path).stat().st_size
        return size_bytes / (1024 * 1024)
    return 0


def clean_dataset(input_file, output_file):
    """
    清洗数据集：去重、过滤无效数据、质量优化
    """
    import re
    from collections import Counter
    from tqdm import tqdm

    print(f"开始清洗数据集: {input_file}")

    # 读取原始数据
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    print(f"原始数据量: {len(lines):,} 条")

    # 解析和清理数据
    cleaned_pairs = []
    seen_pairs = set()

    for line in tqdm(lines, desc="清洗数据"):
        parts = line.strip().split('\t')
        if len(parts) >= 2:
            de_text = clean_text(parts[0])
            zh_text = clean_text(parts[1])

            # 基本验证
            if (de_text and zh_text and
                is_valid_text_pair(de_text, zh_text) and
                (de_text.lower(), zh_text) not in seen_pairs):

                seen_pairs.add((de_text.lower(), zh_text))
                cleaned_pairs.append((de_text, zh_text))

    print(f"清洗后数据量: {len(cleaned_pairs):,} 条")

    # 保存清洗后的数据
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    with open(output_file, 'w', encoding='utf-8') as f:
        for de_text, zh_text in cleaned_pairs:
            f.write(f"{de_text}\t{zh_text}\n")

    print(f"清洗完成！输出文件: {output_file}")
    return len(cleaned_pairs)


def clean_text(text):
    """清理文本"""
    if not text:
        return ""

    import re

    # 移除多余空格
    text = re.sub(r'\s+', ' ', text.strip())

    # 移除特殊字符（保留基本标点）
    text = re.sub(r'[^\w\s.,!?;:()\[\]{}"\'/-äöüßÄÖÜ\u4e00-\u9fff]', '', text)

    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)

    return text.strip()


def is_valid_text_pair(de_text, zh_text):
    """验证德语-中文文本对是否有效"""
    # 长度检查
    if len(de_text) < 2 or len(zh_text) < 1:
        return False

    if len(de_text) > 200 or len(zh_text) > 100:
        return False

    # 德语文本检查
    german_chars = set('abcdefghijklmnopqrstuvwxyzäöüßABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÜ')
    if not any(c in german_chars for c in de_text):
        return False

    # 中文文本检查
    chinese_count = sum(1 for c in zh_text if '\u4e00' <= c <= '\u9fff')
    if chinese_count < len(zh_text) * 0.3:
        return False

    # 长度比例检查
    de_words = len(de_text.split())
    zh_chars = len(zh_text)
    if de_words > 0 and zh_chars > 0:
        ratio = de_words / zh_chars
        if ratio < 0.1 or ratio > 5.0:
            return False

    return True
