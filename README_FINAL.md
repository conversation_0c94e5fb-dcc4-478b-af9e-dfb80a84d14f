# 德语-中文翻译模型训练系统 - 最终版本

## 🎯 项目特性

- ✅ **30000数据集分割**: 自动将大数据集分割为30000条数据的小文件
- ✅ **分离训练**: 教师模型50轮，学生模型100轮，完全独立训练
- ✅ **内存优化**: 支持4GB显存GPU，梯度累积+混合精度训练
- ✅ **统一配置**: 集中管理所有训练参数
- ✅ **自动化流程**: 一键完成数据准备到模型训练的全流程

## 📁 项目结构

```
my-pytorch-deeplearning/
├── main.py                    # 🚀 主训练入口
├── config.py                  # ⚙️ 统一配置文件
├── utils.py                   # 🛠️ 工具函数
├── data_preparation.py        # 📊 数据准备脚本
├── train_teacher_final.py     # 🎓 教师模型训练
├── train_student_final.py     # 🎯 学生模型训练
├── translate.py               # 🔤 翻译测试
├── evaluate_models.py         # 📈 模型评估
├── dataset/dezh.py           # 📚 数据集类
├── model/distillation_transformer.py  # 🧠 模型定义
├── data/de-zh1_split/        # 📂 分割数据 (30000条/文件)
└── train_process/            # 💾 训练输出
    ├── teacher/              # 教师模型
    └── student/              # 学生模型
```

## 🚀 快速开始

### 1. 完整训练流程（推荐）

```bash
# 标准配置 - 教师50轮，学生100轮
python main.py --mode complete --config default

# 4GB显存优化 - 教师20轮，学生40轮
python main.py --mode complete --config 4gb

# 快速测试 - 教师3轮，学生5轮
python main.py --mode complete --config quick
```

### 2. 分步训练

```bash
# 步骤1: 准备数据
python main.py --mode data

# 步骤2: 训练教师模型
python main.py --mode teacher --config default

# 步骤3: 训练学生模型
python main.py --mode student
```

### 3. 检查状态

```bash
# 检查数据准备状态
python main.py --check_data

# 显示使用帮助
python main.py
```

## ⚙️ 配置说明

### 标准配置 (default)
- **教师模型**: 256维, 4层, 4头, 50轮训练
- **学生模型**: 128维, 2层, 4头, 100轮训练
- **批次大小**: 8
- **序列长度**: 32
- **适用**: 8GB+ 显存

### 4GB显存优化 (4gb)
- **教师模型**: 128维, 3层, 4头, 20轮训练
- **学生模型**: 64维, 2层, 2头, 40轮训练
- **批次大小**: 4
- **序列长度**: 24
- **适用**: 4GB 显存

### 快速测试 (quick)
- **教师模型**: 64维, 2层, 2头, 3轮训练
- **学生模型**: 32维, 2层, 2头, 5轮训练
- **批次大小**: 4
- **序列长度**: 20
- **适用**: 快速验证流程

## 📊 数据处理

### 数据集信息
- **原始数据**: 636,317条德语-中文翻译对
- **分割大小**: 30,000条/文件
- **训练集**: 80% (约17个文件)
- **验证集**: 10% (约3个文件)
- **测试集**: 10% (约3个文件)

### 自动数据准备
系统会自动检查并准备所需的数据：
1. 合并德语和中文源文件
2. 分割为30000条数据的小文件
3. 创建训练/验证/测试集

## 🎓 训练流程

### 教师模型训练
1. **数据加载**: 逐个加载分割文件
2. **梯度累积**: 减少内存使用
3. **混合精度**: FP16训练加速
4. **定期保存**: 每10轮保存检查点
5. **最佳模型**: 自动保存最低损失模型

### 学生模型训练
1. **加载教师**: 使用预训练教师模型
2. **知识蒸馏**: 软标签+硬标签损失
3. **参数压缩**: 通常2-4倍参数压缩
4. **性能保持**: 保持90%+的翻译质量

## 📈 监控和评估

### 训练监控
```bash
# 查看教师模型训练日志
tensorboard --logdir train_process/teacher/logs

# 查看学生模型训练日志
tensorboard --logdir train_process/student/logs
```

### 模型测试
```bash
# 翻译测试
python translate.py \
    --teacher_model train_process/teacher/checkpoints/teacher_best.pt \
    --student_model train_process/student/checkpoints/student_best.pt \
    --text "Guten Tag, wie geht es Ihnen?"

# 模型评估
python evaluate_models.py
```

## 🔧 内存优化技术

1. **梯度累积**: 小批次训练，累积梯度更新
2. **混合精度**: 使用FP16减少50%内存使用
3. **数据分割**: 30000条数据分批处理
4. **内存清理**: 定期清理GPU内存碎片
5. **模型压缩**: 学生模型参数大幅减少

## 📋 训练参数对比

| 配置 | GPU内存 | 教师轮次 | 学生轮次 | 训练时间 | 适用场景 |
|------|---------|----------|----------|----------|----------|
| default | 8GB+ | 50 | 100 | 6-12小时 | 生产环境 |
| 4gb | 4GB | 20 | 40 | 2-4小时 | 资源受限 |
| quick | 2GB+ | 3 | 5 | 30分钟 | 快速验证 |

## 🛠️ 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 使用4GB优化配置
   python main.py --mode complete --config 4gb
   ```

2. **数据文件不存在**
   ```bash
   # 重新准备数据
   python main.py --mode data
   ```

3. **教师模型未找到**
   ```bash
   # 先训练教师模型
   python main.py --mode teacher
   ```

4. **训练中断恢复**
   - 检查 `train_process/*/checkpoints/` 目录
   - 使用最新的检查点继续训练

### 性能优化建议

1. **减少批次大小**: 如果内存不足
2. **增加梯度累积**: 保持有效批次大小
3. **使用混合精度**: 显著减少内存使用
4. **定期清理内存**: 避免内存碎片

## 🎉 预期效果

使用本系统训练后，您将获得：

- ✅ **教师模型**: 高质量的德语-中文翻译模型
- ✅ **学生模型**: 压缩后的高效翻译模型
- ✅ **训练日志**: 完整的训练过程记录
- ✅ **评估报告**: 模型性能对比分析

## 📞 支持

如果遇到问题：
1. 检查GPU内存是否足够
2. 确认数据文件是否完整
3. 查看训练日志中的错误信息
4. 尝试使用更小的配置参数

---

**开始训练**: `python main.py --mode complete --config 4gb`
