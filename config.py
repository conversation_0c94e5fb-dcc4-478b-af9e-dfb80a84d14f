"""
统一配置文件
集中管理所有训练参数和路径配置
"""

class Config:
    """训练配置类"""
    
    # 数据配置
    DATA_PATH = "data/de-zh1.txt/dezh1.txt"
    SPLIT_DATA_DIR = "data/de-zh1_split"
    COMPRESSED_DATA_DIR = "data/de-zh1_compressed"  # 压缩后的数据目录
    SPLIT_SIZE = 30000
    
    # 训练配置
    TEACHER_EPOCHS = 50
    STUDENT_EPOCHS = 100
    BATCH_SIZE = 8
    MAX_SEQ_LENGTH = 32
    LEARNING_RATE = 0.0001
    
    # 梯度累积和内存优化
    GRADIENT_ACCUMULATION_STEPS = 4
    USE_AMP = True  # 混合精度训练
    
    # 教师模型配置
    TEACHER_DIM = 256
    TEACHER_LAYERS = 4
    TEACHER_HEADS = 4
    
    # 学生模型配置
    STUDENT_DIM = 128
    STUDENT_LAYERS = 2
    STUDENT_HEADS = 4
    
    # 知识蒸馏配置
    ALPHA = 0.7  # 蒸馏损失权重
    TEMPERATURE = 4.0  # 蒸馏温度
    
    # 输出路径配置
    TEACHER_OUTPUT_DIR = "./train_process/teacher"
    STUDENT_OUTPUT_DIR = "./train_process/student"
    
    # 日志和保存配置
    LOG_EVERY = 100
    SAVE_EVERY = 10  # 每10轮保存一次检查点
    
    # 设备配置
    DEVICE = "auto"  # auto, cuda:0, cpu
    
    @classmethod
    def get_device(cls):
        """获取训练设备"""
        import torch
        if cls.DEVICE == "auto":
            return torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        else:
            return torch.device(cls.DEVICE)
    
    @classmethod
    def print_config(cls):
        """打印配置信息"""
        print("=" * 60)
        print("训练配置")
        print("=" * 60)
        print(f"数据路径: {cls.DATA_PATH}")
        print(f"分割数据目录: {cls.SPLIT_DATA_DIR}")
        print(f"分割大小: {cls.SPLIT_SIZE:,}")
        print(f"教师模型训练轮次: {cls.TEACHER_EPOCHS}")
        print(f"学生模型训练轮次: {cls.STUDENT_EPOCHS}")
        print(f"批次大小: {cls.BATCH_SIZE}")
        print(f"最大序列长度: {cls.MAX_SEQ_LENGTH}")
        print(f"学习率: {cls.LEARNING_RATE}")
        print(f"梯度累积步数: {cls.GRADIENT_ACCUMULATION_STEPS}")
        print(f"混合精度训练: {cls.USE_AMP}")
        print(f"教师模型配置: dim={cls.TEACHER_DIM}, layers={cls.TEACHER_LAYERS}, heads={cls.TEACHER_HEADS}")
        print(f"学生模型配置: dim={cls.STUDENT_DIM}, layers={cls.STUDENT_LAYERS}, heads={cls.STUDENT_HEADS}")
        print(f"蒸馏配置: alpha={cls.ALPHA}, temperature={cls.TEMPERATURE}")
        print(f"设备: {cls.get_device()}")
        print("=" * 60)


class PathConfig:
    """路径配置类"""
    
    # 数据路径
    ORIGINAL_DATA = "data/de-zh1.txt/dezh1.txt"
    SPLIT_DATA_DIR = "data/de-zh1_split"
    
    # 模型保存路径
    TEACHER_MODEL_DIR = "./train_process/teacher"
    STUDENT_MODEL_DIR = "./train_process/student"
    
    # 检查点路径
    TEACHER_BEST_MODEL = "./train_process/teacher/checkpoints/teacher_best.pt"
    STUDENT_BEST_MODEL = "./train_process/student/checkpoints/student_best.pt"
    
    # 日志路径
    TEACHER_LOG_DIR = "./train_process/teacher/logs"
    STUDENT_LOG_DIR = "./train_process/student/logs"
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        from pathlib import Path
        
        dirs = [
            cls.TEACHER_MODEL_DIR + "/checkpoints",
            cls.STUDENT_MODEL_DIR + "/checkpoints",
            cls.TEACHER_LOG_DIR,
            cls.STUDENT_LOG_DIR,
            cls.SPLIT_DATA_DIR
        ]
        
        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)


# 4GB显存优化配置
class Config4GB(Config):
    """4GB显存优化配置 - 专为4GB显存设计"""

    # 内存优化配置
    BATCH_SIZE = 2  # 减小批次大小以适应4GB显存
    MAX_SEQ_LENGTH = 20  # 减小序列长度
    GRADIENT_ACCUMULATION_STEPS = 16  # 增加梯度累积，保持有效批次大小=32

    # 训练配置
    TEACHER_EPOCHS = 20  # 教师模型50轮
    STUDENT_EPOCHS = 40  # 学生模型100轮
    LEARNING_RATE = 0.0002  # 稍微提高学习率

    # 小型模型配置 - 4GB显存优化
    TEACHER_DIM = 128
    TEACHER_LAYERS = 3
    TEACHER_HEADS = 4

    STUDENT_DIM = 64
    STUDENT_LAYERS = 2
    STUDENT_HEADS = 2

    # 内存管理优化
    USE_AMP = True  # 强制使用混合精度
    LOG_EVERY = 200  # 减少日志频率
    SAVE_EVERY = 10  # 每10轮保存检查点

    @classmethod
    def print_config(cls):
        """打印4GB优化配置信息"""
        super().print_config()
        print("🔧 4GB显存优化特性:")
        print(f"  有效批次大小: {cls.BATCH_SIZE} × {cls.GRADIENT_ACCUMULATION_STEPS} = {cls.BATCH_SIZE * cls.GRADIENT_ACCUMULATION_STEPS}")
        print(f"  内存优化: 混合精度 + 梯度累积 + 小批次")
        print(f"  预计GPU内存使用: ~3.5GB")
        print("=" * 60)


# 压缩数据集配置
class Config4GBCompressed(Config4GB):
    """4GB显存优化配置 + 压缩数据集"""

    # 使用压缩后的数据集
    SPLIT_DATA_DIR = "data/de-zh1_compressed"

    @classmethod
    def print_config(cls):
        """打印压缩数据集配置信息"""
        super().print_config()
        print("📦 压缩数据集特性:")
        print(f"  数据目录: {cls.SPLIT_DATA_DIR}")
        print(f"  文件数量: 12个 (压缩自17个)")
        print(f"  数据质量: 高质量去重数据")
        print("=" * 60)


# 快速测试配置
class ConfigQuick(Config):
    """快速测试配置"""
    
    # 快速测试配置
    TEACHER_EPOCHS = 3
    STUDENT_EPOCHS = 5
    BATCH_SIZE = 4
    MAX_SEQ_LENGTH = 20
    
    # 小型模型
    TEACHER_DIM = 64
    TEACHER_LAYERS = 2
    TEACHER_HEADS = 2
    
    STUDENT_DIM = 32
    STUDENT_LAYERS = 2
    STUDENT_HEADS = 2
