"""
数据准备脚本 - 最终版本
处理数据集创建和30000数据分割
"""
import os
import argparse
from pathlib import Path
from config import Config
from utils import split_dataset


def prepare_dezh1_dataset():
    """
    将新的德语和中文文件合并成tab分隔的训练数据文件
    """
    # 文件路径
    de_file = "data/de-zh1.txt/XLEnt.de-zh.de"
    zh_file = "data/de-zh1.txt/XLEnt.de-zh.zh"
    output_file = "data/de-zh1.txt/dezh1.txt"
    
    print("开始处理德语-中文数据集...")
    
    # 检查输入文件是否存在
    if not os.path.exists(de_file):
        print(f"错误：德语文件 {de_file} 不存在")
        return False
    
    if not os.path.exists(zh_file):
        print(f"错误：中文文件 {zh_file} 不存在")
        return False
    
    # 读取德语文件
    with open(de_file, 'r', encoding='utf-8') as f:
        de_lines = [line.strip() for line in f.readlines()]
    
    # 读取中文文件
    with open(zh_file, 'r', encoding='utf-8') as f:
        zh_lines = [line.strip() for line in f.readlines()]
    
    print(f"德语句子数量: {len(de_lines):,}")
    print(f"中文句子数量: {len(zh_lines):,}")
    
    # 确保两个文件的行数相同
    if len(de_lines) != len(zh_lines):
        print("警告：德语和中文文件的行数不匹配")
        min_lines = min(len(de_lines), len(zh_lines))
        de_lines = de_lines[:min_lines]
        zh_lines = zh_lines[:min_lines]
        print(f"使用前 {min_lines:,} 行数据")
    
    # 合并数据并写入输出文件
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for de_line, zh_line in zip(de_lines, zh_lines):
            # 过滤掉空行
            if de_line.strip() and zh_line.strip():
                f.write(f"{de_line}\t{zh_line}\n")
    
    print(f"数据处理完成！输出文件: {output_file}")
    
    # 显示前几行示例
    print("\n前5行示例:")
    with open(output_file, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i >= 5:
                break
            parts = line.strip().split('\t')
            if len(parts) >= 2:
                print(f"德语: {parts[0]}")
                print(f"中文: {parts[1]}")
                print("-" * 50)
    
    return True


def prepare_split_dataset(clean_data=True):
    """
    分割数据集为30000条数据的小文件
    """
    config = Config()

    # 检查原始数据集是否存在
    if not Path(config.DATA_PATH).exists():
        print(f"原始数据集不存在: {config.DATA_PATH}")
        print("正在创建原始数据集...")
        if not prepare_dezh1_dataset():
            return False

    # 数据清洗
    if clean_data:
        cleaned_data_path = "data/de-zh1.txt/dezh1_cleaned.txt"
        if not Path(cleaned_data_path).exists():
            print("开始数据清洗...")
            from utils import clean_dataset
            clean_dataset(config.DATA_PATH, cleaned_data_path)
        else:
            print(f"清洗后数据已存在: {cleaned_data_path}")

        data_path = cleaned_data_path
    else:
        data_path = config.DATA_PATH

    # 分割数据集
    print(f"\n开始分割数据集，分割大小: {config.SPLIT_SIZE:,}")
    split_dataset(
        input_file=data_path,
        output_dir=config.SPLIT_DATA_DIR,
        split_size=config.SPLIT_SIZE,
        train_ratio=0.8,
        val_ratio=0.1,
        test_ratio=0.1
    )

    return True


def check_data_status():
    """
    检查数据准备状态
    """
    config = Config()
    
    print("=" * 60)
    print("数据准备状态检查")
    print("=" * 60)
    
    # 检查原始数据集
    original_exists = Path(config.DATA_PATH).exists()
    print(f"原始数据集 ({config.DATA_PATH}): {'✅ 存在' if original_exists else '❌ 不存在'}")
    
    if original_exists:
        with open(config.DATA_PATH, 'r', encoding='utf-8') as f:
            line_count = sum(1 for _ in f)
        print(f"  数据量: {line_count:,} 条")
    
    # 检查分割数据集
    split_dir = Path(config.SPLIT_DATA_DIR)
    split_exists = split_dir.exists()
    print(f"分割数据集目录 ({config.SPLIT_DATA_DIR}): {'✅ 存在' if split_exists else '❌ 不存在'}")
    
    if split_exists:
        train_files = list((split_dir / "train").glob("*.txt"))
        val_files = list((split_dir / "val").glob("*.txt"))
        test_files = list((split_dir / "test").glob("*.txt"))
        
        print(f"  训练文件数: {len(train_files)}")
        print(f"  验证文件数: {len(val_files)}")
        print(f"  测试文件数: {len(test_files)}")
        
        if train_files:
            total_train_lines = 0
            for file_path in train_files:
                with open(file_path, 'r', encoding='utf-8') as f:
                    total_train_lines += sum(1 for _ in f)
            print(f"  训练数据总量: {total_train_lines:,} 条")
    
    # 检查源文件
    de_file = "data/de-zh1.txt/XLEnt.de-zh.de"
    zh_file = "data/de-zh1.txt/XLEnt.de-zh.zh"
    
    de_exists = Path(de_file).exists()
    zh_exists = Path(zh_file).exists()
    
    print(f"德语源文件 ({de_file}): {'✅ 存在' if de_exists else '❌ 不存在'}")
    print(f"中文源文件 ({zh_file}): {'✅ 存在' if zh_exists else '❌ 不存在'}")
    
    print("=" * 60)
    
    # 给出建议
    if not original_exists:
        print("建议: 运行 python data_preparation.py --prepare-original")
    elif not split_exists:
        print("建议: 运行 python data_preparation.py --prepare-split")
    else:
        print("✅ 数据准备完成，可以开始训练")
        print("  教师模型训练: python train_teacher_final.py")
        print("  学生模型训练: python train_student_final.py")


def main():
    parser = argparse.ArgumentParser(description='数据准备工具')
    parser.add_argument('--prepare-original', action='store_true', help='准备原始数据集')
    parser.add_argument('--prepare-split', action='store_true', help='准备分割数据集')
    parser.add_argument('--check-status', action='store_true', help='检查数据状态')
    parser.add_argument('--prepare-all', action='store_true', help='准备所有数据')
    
    args = parser.parse_args()
    
    if args.prepare_original:
        prepare_dezh1_dataset()
    elif args.prepare_split:
        prepare_split_dataset()
    elif args.check_status:
        check_data_status()
    elif args.prepare_all:
        print("开始准备所有数据...")
        if prepare_dezh1_dataset():
            prepare_split_dataset()
        check_data_status()
    else:
        # 默认行为：检查状态并根据需要准备数据
        check_data_status()
        
        config = Config()
        if not Path(config.DATA_PATH).exists():
            print("\n自动准备原始数据集...")
            prepare_dezh1_dataset()
        
        if not Path(config.SPLIT_DATA_DIR).exists():
            print("\n自动准备分割数据集...")
            prepare_split_dataset()
        
        print("\n最终状态:")
        check_data_status()


if __name__ == "__main__":
    main()
