import torch
import time
import numpy as np
from pathlib import Path
from dataset.dezh import DeZhTranslationDataset
from torch.utils.data import DataLoader
from torch.nn.functional import pad
import matplotlib.pyplot as plt
import seaborn as sns

# 配置
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
data_dir = "data/de-zh1.txt/dezh1.txt"
max_seq_length = 48
batch_size = 16


def collate_fn(batch):
    """数据批处理函数"""
    bs_id = 0  # <bos> index
    eos_id = 1  # <eos> index
    pad_id = 2  # <pad> index

    src_list, tgt_list = [], []

    for _src, _tgt in batch:
        src_tensor = torch.tensor(_src[:max_seq_length - 2], dtype=torch.int64)
        tgt_tensor = torch.tensor(_tgt[:max_seq_length - 2], dtype=torch.int64)

        processed_src = torch.cat([
            torch.tensor([bs_id], dtype=torch.int64),
            src_tensor,
            torch.tensor([eos_id], dtype=torch.int64)
        ])

        processed_tgt = torch.cat([
            torch.tensor([bs_id], dtype=torch.int64),
            tgt_tensor,
            torch.tensor([eos_id], dtype=torch.int64)
        ])

        src_list.append(pad(
            processed_src,
            (0, max_seq_length - len(processed_src)),
            value=pad_id
        ))

        tgt_list.append(pad(
            processed_tgt,
            (0, max_seq_length - len(processed_tgt)),
            value=pad_id
        ))

    src = torch.stack(src_list).to(device)
    tgt = torch.stack(tgt_list).to(device)
    tgt_y = tgt[:, 1:]
    tgt = tgt[:, :-1]

    return src, tgt, tgt_y


def evaluate_model(model_path, model_name, num_batches=50):
    """评估模型性能"""
    print(f"\n评估 {model_name}...")
    
    # 加载模型
    try:
        model = torch.load(model_path, map_location=device, weights_only=False)
        model.to(device)
        model.eval()
    except Exception as e:
        print(f"无法加载模型 {model_path}: {e}")
        return None
    
    # 加载数据
    dataset = DeZhTranslationDataset(data_dir)
    # 使用数据集的一个子集进行评估
    subset_indices = torch.randperm(len(dataset))[:num_batches * batch_size]
    subset_dataset = torch.utils.data.Subset(dataset, subset_indices)
    data_loader = DataLoader(subset_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)
    
    # 评估指标
    total_loss = 0
    total_tokens = 0
    inference_times = []
    
    criterion = torch.nn.CrossEntropyLoss(ignore_index=2)
    
    with torch.no_grad():
        for batch_idx, (src, tgt, tgt_y) in enumerate(data_loader):
            if batch_idx >= num_batches:
                break
                
            start_time = time.time()
            
            # 前向传播
            if hasattr(model, 'forward') and len(model.forward.__code__.co_varnames) > 3:
                # 蒸馏模型返回两个值
                out, logits = model(src, tgt)
            else:
                # 原始模型只返回一个值
                out = model(src, tgt)
                logits = model.predictor(out)
            
            end_time = time.time()
            inference_times.append(end_time - start_time)
            
            # 计算损失
            loss = criterion(
                logits.contiguous().view(-1, logits.size(-1)),
                tgt_y.contiguous().view(-1)
            )
            
            total_loss += loss.item()
            total_tokens += (tgt_y != 2).sum().item()
    
    # 计算指标
    avg_loss = total_loss / num_batches
    perplexity = torch.exp(torch.tensor(avg_loss)).item()
    avg_inference_time = np.mean(inference_times)
    throughput = batch_size / avg_inference_time  # 句子/秒
    
    # 模型大小
    model_size = sum(p.numel() for p in model.parameters())
    
    results = {
        'model_name': model_name,
        'avg_loss': avg_loss,
        'perplexity': perplexity,
        'avg_inference_time': avg_inference_time,
        'throughput': throughput,
        'model_size': model_size,
        'inference_times': inference_times
    }
    
    print(f"{model_name} 评估结果:")
    print(f"  平均损失: {avg_loss:.4f}")
    print(f"  困惑度: {perplexity:.4f}")
    print(f"  平均推理时间: {avg_inference_time:.4f}秒/批次")
    print(f"  吞吐量: {throughput:.2f}句子/秒")
    print(f"  模型大小: {model_size:,}参数")
    
    return results


def compare_models():
    """比较不同模型的性能"""
    models_to_evaluate = [
        ("train_process/transformer-dezh-improved/transformer_checkpoints/best.pt", "改进基础模型"),
        ("train_process/teacher-student-dezh/checkpoints/teacher_best.pt", "教师模型"),
        ("train_process/teacher-student-dezh/checkpoints/student_best.pt", "学生模型"),
    ]
    
    results = []
    
    for model_path, model_name in models_to_evaluate:
        if Path(model_path).exists():
            result = evaluate_model(model_path, model_name)
            if result:
                results.append(result)
        else:
            print(f"模型文件不存在: {model_path}")
    
    if len(results) < 2:
        print("需要至少两个模型进行比较")
        return
    
    # 创建比较表格
    print("\n" + "="*80)
    print("模型性能比较")
    print("="*80)
    print(f"{'模型名称':<15} {'损失':<10} {'困惑度':<10} {'推理时间(s)':<12} {'吞吐量(句/s)':<12} {'参数数量':<15}")
    print("-"*80)
    
    for result in results:
        print(f"{result['model_name']:<15} "
              f"{result['avg_loss']:<10.4f} "
              f"{result['perplexity']:<10.2f} "
              f"{result['avg_inference_time']:<12.4f} "
              f"{result['throughput']:<12.2f} "
              f"{result['model_size']:<15,}")
    
    # 计算压缩比和加速比
    if len(results) >= 2:
        teacher_result = None
        student_result = None
        
        for result in results:
            if "教师" in result['model_name']:
                teacher_result = result
            elif "学生" in result['model_name']:
                student_result = result
        
        if teacher_result and student_result:
            compression_ratio = teacher_result['model_size'] / student_result['model_size']
            speedup = teacher_result['avg_inference_time'] / student_result['avg_inference_time']
            
            print("\n知识蒸馏效果:")
            print(f"参数压缩比: {compression_ratio:.2f}x")
            print(f"推理加速比: {speedup:.2f}x")
            print(f"困惑度差异: {student_result['perplexity'] - teacher_result['perplexity']:.2f}")
    
    # 绘制性能图表
    plot_performance_comparison(results)


def plot_performance_comparison(results):
    """绘制性能比较图表"""
    if len(results) < 2:
        return
    
    # 设置图表样式
    plt.style.use('seaborn-v0_8')
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    model_names = [r['model_name'] for r in results]
    
    # 1. 困惑度比较
    perplexities = [r['perplexity'] for r in results]
    ax1.bar(model_names, perplexities, color=['skyblue', 'lightcoral', 'lightgreen'][:len(results)])
    ax1.set_title('困惑度比较 (越低越好)')
    ax1.set_ylabel('困惑度')
    ax1.tick_params(axis='x', rotation=45)
    
    # 2. 推理时间比较
    inference_times = [r['avg_inference_time'] for r in results]
    ax2.bar(model_names, inference_times, color=['skyblue', 'lightcoral', 'lightgreen'][:len(results)])
    ax2.set_title('推理时间比较 (越低越好)')
    ax2.set_ylabel('时间 (秒)')
    ax2.tick_params(axis='x', rotation=45)
    
    # 3. 模型大小比较
    model_sizes = [r['model_size'] / 1e6 for r in results]  # 转换为百万参数
    ax3.bar(model_names, model_sizes, color=['skyblue', 'lightcoral', 'lightgreen'][:len(results)])
    ax3.set_title('模型大小比较')
    ax3.set_ylabel('参数数量 (百万)')
    ax3.tick_params(axis='x', rotation=45)
    
    # 4. 吞吐量比较
    throughputs = [r['throughput'] for r in results]
    ax4.bar(model_names, throughputs, color=['skyblue', 'lightcoral', 'lightgreen'][:len(results)])
    ax4.set_title('吞吐量比较 (越高越好)')
    ax4.set_ylabel('句子/秒')
    ax4.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n性能比较图表已保存为: model_performance_comparison.png")


def benchmark_inference_speed():
    """基准测试推理速度"""
    print("\n进行推理速度基准测试...")
    
    # 测试不同序列长度的推理速度
    test_sentences = [
        "Hallo",  # 短句
        "Guten Morgen, wie geht es dir?",  # 中等长度
        "Am Anfang schuf Gott Himmel und Erde, und die Erde war wüst und leer.",  # 长句
    ]
    
    models_to_test = [
        ("train_process/teacher-student-dezh/checkpoints/teacher_best.pt", "教师模型"),
        ("train_process/teacher-student-dezh/checkpoints/student_best.pt", "学生模型"),
    ]
    
    dataset = DeZhTranslationDataset(data_dir)
    
    for model_path, model_name in models_to_test:
        if not Path(model_path).exists():
            continue
            
        print(f"\n测试 {model_name}:")
        model = torch.load(model_path, map_location=device, weights_only=False)
        model.to(device)
        model.eval()
        
        for sentence in test_sentences:
            # 预处理
            src_tokens = [0] + dataset.de_vocab(dataset.de_tokenizer(sentence)) + [1]
            src_tensor = torch.tensor(src_tokens).unsqueeze(0).to(device)
            tgt_tensor = torch.tensor([[0]]).to(device)
            
            # 测试推理时间
            times = []
            for _ in range(10):  # 多次测试取平均
                start_time = time.time()
                with torch.no_grad():
                    for _ in range(min(20, max_seq_length)):
                        if hasattr(model, 'forward') and len(model.forward.__code__.co_varnames) > 3:
                            out, logits = model(src_tensor, tgt_tensor)
                            predict = logits[:, -1]
                        else:
                            out = model(src_tensor, tgt_tensor)
                            predict = model.predictor(out[:, -1])
                        
                        next_token = torch.argmax(predict, dim=1)
                        tgt_tensor = torch.cat([tgt_tensor, next_token.unsqueeze(0)], dim=1)
                        
                        if next_token.item() == 1:
                            break
                
                end_time = time.time()
                times.append(end_time - start_time)
            
            avg_time = np.mean(times)
            print(f"  '{sentence}' -> 平均推理时间: {avg_time:.4f}秒")


if __name__ == "__main__":
    print("开始模型评估...")
    print(f"使用设备: {device}")
    
    # 比较模型性能
    compare_models()
    
    # 基准测试推理速度
    benchmark_inference_speed()
