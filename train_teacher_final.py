"""
教师模型训练脚本 - 最终版本
支持30000数据集分割，50轮训练，内存优化
"""
import os
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm
from pathlib import Path
import glob
import argparse

from dataset.dezh import DeZhTranslationDataset
from model.distillation_transformer import TeacherTransformerModel
from config import Config, PathConfig
from utils import collate_fn, clear_memory, print_training_info, save_model_safely, create_optimizer, create_scaler


def train_on_split_files(model, train_files, criterion, optimizer, scaler, config, epoch, writer, global_step):
    """在分割文件上训练"""
    model.train()
    total_loss = 0
    
    for file_idx, train_file in enumerate(train_files):
        print(f"处理文件 {file_idx+1}/{len(train_files)}: {Path(train_file).name}")
        
        # 加载当前文件的数据
        file_dataset = DeZhTranslationDataset(train_file)
        file_loader = DataLoader(
            file_dataset, 
            batch_size=config.BATCH_SIZE, 
            shuffle=True, 
            collate_fn=lambda batch: collate_fn(batch, config.MAX_SEQ_LENGTH, config.get_device())
        )
        
        file_loss = 0
        loop = tqdm(file_loader, desc=f"Epoch {epoch+1} File {file_idx+1}/{len(train_files)}")
        
        for batch_idx, (src, tgt) in enumerate(loop):
            # 梯度累积
            if batch_idx % config.GRADIENT_ACCUMULATION_STEPS == 0:
                optimizer.zero_grad()
            
            # 准备输入和目标
            tgt_input = tgt[:, :-1]
            tgt_output = tgt[:, 1:]
            
            # 前向传播
            if config.USE_AMP and scaler is not None:
                try:
                    # 尝试新版本API
                    with torch.amp.autocast('cuda'):
                        _, logits = model(src, tgt_input)
                        loss = criterion(logits.reshape(-1, logits.size(-1)), tgt_output.reshape(-1))
                        loss = loss / config.GRADIENT_ACCUMULATION_STEPS
                except AttributeError:
                    # 回退到旧版本API
                    with torch.cuda.amp.autocast():
                        _, logits = model(src, tgt_input)
                        loss = criterion(logits.reshape(-1, logits.size(-1)), tgt_output.reshape(-1))
                        loss = loss / config.GRADIENT_ACCUMULATION_STEPS
            else:
                _, logits = model(src, tgt_input)
                loss = criterion(logits.reshape(-1, logits.size(-1)), tgt_output.reshape(-1))
                loss = loss / config.GRADIENT_ACCUMULATION_STEPS

            # 反向传播
            if config.USE_AMP and scaler is not None:
                scaler.scale(loss).backward()
            else:
                loss.backward()
            
            # 梯度累积后更新
            if (batch_idx + 1) % config.GRADIENT_ACCUMULATION_STEPS == 0:
                if config.USE_AMP and scaler is not None:
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()
            
            file_loss += loss.item() * config.GRADIENT_ACCUMULATION_STEPS
            
            # 记录日志
            if global_step % config.LOG_EVERY == 0:
                writer.add_scalar('Loss/Train', loss.item() * config.GRADIENT_ACCUMULATION_STEPS, global_step)
            
            loop.set_postfix({'loss': loss.item() * config.GRADIENT_ACCUMULATION_STEPS})
            global_step += 1
            
            # 定期清理内存
            if batch_idx % 100 == 0:
                clear_memory()
        
        total_loss += file_loss / len(file_loader)
        
        # 清理当前文件的数据
        del file_dataset, file_loader
        clear_memory()
    
    return total_loss / len(train_files), global_step


def train_teacher(config_class=Config):
    """训练教师模型"""
    config = config_class()
    device = config.get_device()
    
    print("=" * 60)
    print("教师模型训练")
    print("=" * 60)
    config.print_config()
    
    # 创建目录
    PathConfig.create_directories()
    
    # 检查压缩数据集
    compressed_data_dir = "data/de-zh1_compressed"
    train_files = sorted(glob.glob(f"{compressed_data_dir}/train/*.txt"))

    if not train_files:
        print(f"错误：在 {compressed_data_dir}/train/ 中未找到训练文件")
        print("请先运行数据压缩：python compress_dataset.py")
        return None
    
    print(f"找到 {len(train_files)} 个训练文件")
    
    # 获取词汇表（从第一个文件）
    temp_dataset = DeZhTranslationDataset(train_files[0])
    de_vocab = temp_dataset.de_vocab
    zh_vocab = temp_dataset.zh_vocab
    del temp_dataset
    clear_memory()
    
    # 创建教师模型
    teacher_model = TeacherTransformerModel(
        d_model=config.TEACHER_DIM,
        src_vocab=de_vocab,
        tgt_vocab=zh_vocab,
        max_seq_length=config.MAX_SEQ_LENGTH,
        device=device,
        num_layers=config.TEACHER_LAYERS,
        num_heads=config.TEACHER_HEADS
    ).to(device)
    
    # 打印训练信息
    total_samples = sum(len(open(f, 'r', encoding='utf-8').readlines()) for f in train_files)
    print_training_info("教师", teacher_model, total_samples, config.BATCH_SIZE, config.TEACHER_EPOCHS)
    
    # 训练设置
    criterion = nn.CrossEntropyLoss(ignore_index=2)
    optimizer = create_optimizer(teacher_model, config.LEARNING_RATE)
    scaler = create_scaler(config.USE_AMP)
    writer = SummaryWriter(config.TEACHER_OUTPUT_DIR + "/logs")
    
    best_loss = float('inf')
    global_step = 0
    
    for epoch in range(config.TEACHER_EPOCHS):
        print(f"\n开始第 {epoch+1}/{config.TEACHER_EPOCHS} 轮训练")
        
        epoch_loss, global_step = train_on_split_files(
            teacher_model, train_files, criterion, optimizer, scaler, 
            config, epoch, writer, global_step
        )
        
        print(f"教师模型 Epoch {epoch+1} 平均损失: {epoch_loss:.4f}")
        
        # 保存最佳模型
        if epoch_loss < best_loss:
            model_path = Path(config.TEACHER_OUTPUT_DIR) / "checkpoints" / "teacher_best.pt"
            if save_model_safely(teacher_model, model_path):
                best_loss = epoch_loss
                print(f"新的最佳教师模型已保存，损失: {best_loss:.4f}")
        
        # 定期保存检查点
        if (epoch + 1) % config.SAVE_EVERY == 0:
            checkpoint_path = Path(config.TEACHER_OUTPUT_DIR) / "checkpoints" / f"teacher_epoch_{epoch+1}.pt"
            save_model_safely(teacher_model, checkpoint_path)
            print(f"检查点已保存: teacher_epoch_{epoch+1}.pt")
    
    writer.close()
    print("\n教师模型训练完成！")
    print(f"最佳模型: {config.TEACHER_OUTPUT_DIR}/checkpoints/teacher_best.pt")
    
    return teacher_model


def main():
    parser = argparse.ArgumentParser(description='教师模型训练')
    parser.add_argument('--config', choices=['default', '4gb', 'compressed', 'quick'], default='4gb',
                       help='配置类型: default=标准配置, 4gb=4GB显存优化, compressed=压缩数据集, quick=快速测试')
    parser.add_argument('--clean_data', action='store_true', default=True,
                       help='是否使用清洗后的数据')

    args = parser.parse_args()

    # 选择配置
    if args.config == '4gb':
        from config import Config4GB
        config_class = Config4GB
    elif args.config == 'compressed':
        from config import Config4GBCompressed
        config_class = Config4GBCompressed
    elif args.config == 'quick':
        from config import ConfigQuick
        config_class = ConfigQuick
    else:
        config_class = Config

    print(f"使用配置: {args.config}")
    print(f"数据清洗: {'是' if args.clean_data else '否'}")

    # 开始训练
    train_teacher(config_class)


if __name__ == "__main__":
    main()
