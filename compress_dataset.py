"""
数据集压缩脚本
清洗、去重、减少数据量，将17个文件压缩到12个
"""
import os
import re
import glob
from pathlib import Path
from collections import Counter, defaultdict
from tqdm import tqdm
import argparse
import random


class DatasetCompressor:
    """数据集压缩器"""
    
    def __init__(self):
        self.seen_pairs = set()
        self.quality_scores = {}
        
    def clean_text(self, text):
        """清理文本"""
        if not text:
            return ""
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符（保留基本标点）
        text = re.sub(r'[^\w\s.,!?;:()\[\]{}"\'/-äöüßÄÖÜ\u4e00-\u9fff]', '', text)
        
        # 移除HTML标签和URL
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'http[s]?://\S+', '', text)
        
        return text.strip()
    
    def is_valid_german(self, text):
        """检查是否为有效的德语文本"""
        if not text or len(text.strip()) < 3:
            return False
        
        # 检查长度
        if len(text) > 150 or len(text) < 3:
            return False
        
        # 检查是否包含德语字符
        german_chars = set('abcdefghijklmnopqrstuvwxyzäöüßABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÜ')
        if not any(c in german_chars for c in text):
            return False
        
        # 检查单词数量
        words = text.split()
        if len(words) < 2 or len(words) > 25:
            return False
        
        # 检查是否包含过多数字
        digit_count = sum(1 for c in text if c.isdigit())
        if digit_count > len(text) * 0.3:
            return False
        
        return True
    
    def is_valid_chinese(self, text):
        """检查是否为有效的中文文本"""
        if not text or len(text.strip()) < 1:
            return False
        
        # 检查长度
        if len(text) > 80 or len(text) < 1:
            return False
        
        # 检查中文字符比例
        chinese_count = sum(1 for c in text if '\u4e00' <= c <= '\u9fff')
        if chinese_count < len(text) * 0.4:
            return False
        
        return True
    
    def calculate_quality_score(self, de_text, zh_text):
        """计算文本对的质量分数"""
        score = 0.0
        
        # 长度分数（偏好中等长度）
        de_words = len(de_text.split())
        zh_chars = len(zh_text)
        
        # 德语单词数量分数（5-15个单词最佳）
        if 5 <= de_words <= 15:
            score += 2.0
        elif 3 <= de_words <= 20:
            score += 1.0
        else:
            score += 0.5
        
        # 中文字符数量分数（5-30个字符最佳）
        if 5 <= zh_chars <= 30:
            score += 2.0
        elif 2 <= zh_chars <= 50:
            score += 1.0
        else:
            score += 0.5
        
        # 长度比例分数
        if de_words > 0 and zh_chars > 0:
            ratio = de_words / zh_chars
            if 0.3 <= ratio <= 2.0:
                score += 1.5
            elif 0.2 <= ratio <= 3.0:
                score += 1.0
            else:
                score += 0.2
        
        # 字符多样性分数
        de_unique_chars = len(set(de_text.lower()))
        zh_unique_chars = len(set(zh_text))
        
        if de_unique_chars > len(de_text) * 0.3:
            score += 0.5
        if zh_unique_chars > len(zh_text) * 0.4:
            score += 0.5
        
        # 标点符号分数（适量标点符号表示完整句子）
        de_punct = sum(1 for c in de_text if c in '.,!?;:')
        if 1 <= de_punct <= 3:
            score += 0.5
        
        return score
    
    def process_file(self, file_path):
        """处理单个文件"""
        valid_pairs = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            parts = line.strip().split('\t')
            if len(parts) >= 2:
                de_text = self.clean_text(parts[0])
                zh_text = self.clean_text(parts[1])
                
                # 验证文本质量
                if (self.is_valid_german(de_text) and 
                    self.is_valid_chinese(zh_text)):
                    
                    # 去重检查
                    pair_key = (de_text.lower().strip(), zh_text.strip())
                    if pair_key not in self.seen_pairs:
                        self.seen_pairs.add(pair_key)
                        
                        # 计算质量分数
                        quality_score = self.calculate_quality_score(de_text, zh_text)
                        valid_pairs.append((de_text, zh_text, quality_score))
        
        return valid_pairs
    
    def compress_dataset(self, input_dir, output_dir, target_files=12, quality_threshold=3.0):
        """压缩数据集"""
        print(f"开始压缩数据集: {input_dir} -> {output_dir}")
        print(f"目标文件数: {target_files}")
        
        # 获取所有训练文件
        train_files = sorted(glob.glob(f"{input_dir}/train/*.txt"))
        if not train_files:
            print(f"错误：在 {input_dir}/train/ 中未找到训练文件")
            return False
        
        print(f"找到 {len(train_files)} 个训练文件")
        
        # 处理所有文件
        all_pairs = []
        total_original = 0
        
        for file_path in tqdm(train_files, desc="处理文件"):
            pairs = self.process_file(file_path)
            all_pairs.extend(pairs)
            
            # 统计原始数据量
            with open(file_path, 'r', encoding='utf-8') as f:
                total_original += len(f.readlines())
        
        print(f"原始数据量: {total_original:,} 条")
        print(f"去重后数据量: {len(all_pairs):,} 条")
        print(f"去重率: {(1 - len(all_pairs)/total_original)*100:.1f}%")
        
        # 按质量分数排序
        all_pairs.sort(key=lambda x: x[2], reverse=True)
        
        # 过滤低质量数据
        high_quality_pairs = [pair for pair in all_pairs if pair[2] >= quality_threshold]
        print(f"高质量数据量: {len(high_quality_pairs):,} 条 (质量分数 >= {quality_threshold})")
        
        # 如果高质量数据太少，降低阈值
        if len(high_quality_pairs) < len(all_pairs) * 0.6:
            quality_threshold = 2.5
            high_quality_pairs = [pair for pair in all_pairs if pair[2] >= quality_threshold]
            print(f"调整质量阈值到 {quality_threshold}，数据量: {len(high_quality_pairs):,} 条")
        
        # 计算每个文件的数据量
        total_pairs = len(high_quality_pairs)
        pairs_per_file = total_pairs // target_files
        
        print(f"每个文件约 {pairs_per_file:,} 条数据")
        
        # 创建输出目录
        output_path = Path(output_dir)
        train_output_dir = output_path / "train"
        train_output_dir.mkdir(parents=True, exist_ok=True)
        
        # 分割并保存数据
        for i in range(target_files):
            start_idx = i * pairs_per_file
            if i == target_files - 1:  # 最后一个文件包含剩余所有数据
                end_idx = total_pairs
            else:
                end_idx = (i + 1) * pairs_per_file
            
            file_pairs = high_quality_pairs[start_idx:end_idx]
            
            # 保存文件
            output_file = train_output_dir / f"train_{i+1:03d}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                for de_text, zh_text, _ in file_pairs:
                    f.write(f"{de_text}\t{zh_text}\n")
            
            print(f"保存 {output_file.name}: {len(file_pairs):,} 条数据")
        
        # 复制验证集和测试集（如果存在）
        for subset in ['val', 'test']:
            subset_files = glob.glob(f"{input_dir}/{subset}/*.txt")
            if subset_files:
                subset_output_dir = output_path / subset
                subset_output_dir.mkdir(exist_ok=True)
                
                print(f"\n处理{subset}集...")
                subset_pairs = []
                
                for file_path in subset_files:
                    pairs = self.process_file(file_path)
                    subset_pairs.extend(pairs)
                
                # 按质量排序并保存
                subset_pairs.sort(key=lambda x: x[2], reverse=True)
                high_quality_subset = [pair for pair in subset_pairs if pair[2] >= quality_threshold]
                
                # 保存为单个文件
                output_file = subset_output_dir / f"{subset}_001.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    for de_text, zh_text, _ in high_quality_subset:
                        f.write(f"{de_text}\t{zh_text}\n")
                
                print(f"保存 {output_file.name}: {len(high_quality_subset):,} 条数据")
        
        # 创建数据集信息文件
        info_file = output_path / "dataset_info.txt"
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write(f"压缩后数据集信息\n")
            f.write(f"原始文件数: {len(train_files)}\n")
            f.write(f"压缩后文件数: {target_files}\n")
            f.write(f"原始数据量: {total_original:,}\n")
            f.write(f"压缩后数据量: {total_pairs:,}\n")
            f.write(f"压缩率: {(1 - total_pairs/total_original)*100:.1f}%\n")
            f.write(f"质量阈值: {quality_threshold}\n")
            f.write(f"每文件数据量: ~{pairs_per_file:,}\n")
        
        print(f"\n✅ 数据集压缩完成！")
        print(f"输出目录: {output_dir}")
        print(f"压缩率: {(1 - total_pairs/total_original)*100:.1f}%")
        print(f"数据集信息: {info_file}")
        
        return True
    
    def show_sample_data(self, output_dir, num_samples=5):
        """显示压缩后的样本数据"""
        train_files = sorted(glob.glob(f"{output_dir}/train/*.txt"))
        if not train_files:
            return
        
        print(f"\n📝 压缩后数据样例 (来自 {train_files[0]}):")
        
        with open(train_files[0], 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for i, line in enumerate(lines[:num_samples]):
            parts = line.strip().split('\t')
            if len(parts) >= 2:
                print(f"  {i+1}. 德语: {parts[0]}")
                print(f"     中文: {parts[1]}")
                print()


def main():
    parser = argparse.ArgumentParser(description='数据集压缩工具')
    parser.add_argument('--input_dir', default='data/de-zh1_split', help='输入数据目录')
    parser.add_argument('--output_dir', default='data/de-zh1_compressed', help='输出数据目录')
    parser.add_argument('--target_files', type=int, default=12, help='目标文件数量')
    parser.add_argument('--quality_threshold', type=float, default=3.0, help='质量分数阈值')
    parser.add_argument('--show_samples', action='store_true', help='显示样本数据')
    
    args = parser.parse_args()
    
    # 检查输入目录
    if not Path(args.input_dir).exists():
        print(f"错误：输入目录不存在 {args.input_dir}")
        print("请先运行: python data_preparation.py")
        return
    
    # 创建压缩器
    compressor = DatasetCompressor()
    
    # 执行压缩
    success = compressor.compress_dataset(
        args.input_dir, 
        args.output_dir, 
        args.target_files,
        args.quality_threshold
    )
    
    if success and args.show_samples:
        compressor.show_sample_data(args.output_dir)
    
    if success:
        print(f"\n💡 下一步操作:")
        print(f"   1. 使用压缩后的数据训练:")
        print(f"      python train_compatible.py --mode both --config 4gb")
        print(f"   2. 更新配置文件中的数据路径:")
        print(f"      SPLIT_DATA_DIR = '{args.output_dir}'")


if __name__ == "__main__":
    main()
