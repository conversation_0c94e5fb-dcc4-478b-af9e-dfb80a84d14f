"""
主训练脚本 - 最终版本
统一的训练入口，支持完整的分离训练流程
"""
import argparse
import sys
from pathlib import Path

from config import Config, Config4GB, ConfigQuick
from data_preparation import check_data_status, prepare_split_dataset
from train_teacher_final import train_teacher
from train_student_final import train_student
from utils import check_gpu_memory


def run_complete_training(config_class=Config):
    """
    运行完整的训练流程：数据准备 -> 教师模型训练 -> 学生模型训练
    """
    config = config_class()
    
    print("🚀 开始完整训练流程")
    print("=" * 60)
    config.print_config()
    
    # 检查GPU内存
    gpu_memory = check_gpu_memory()
    if gpu_memory < 4 and config_class == Config:
        print("⚠️  警告: GPU内存不足4GB，建议使用4GB优化配置")
        print("使用命令: python main.py --config 4gb")
        response = input("是否继续使用当前配置？(y/N): ")
        if response.lower() != 'y':
            return
    
    # 步骤1: 检查和准备数据
    print("\n📊 步骤1: 数据准备")
    print("-" * 40)
    
    if not Path(config.SPLIT_DATA_DIR).exists():
        print("分割数据集不存在，开始准备...")
        if not prepare_split_dataset():
            print("❌ 数据准备失败")
            return
    else:
        print("✅ 分割数据集已存在")
    
    # 步骤2: 训练教师模型
    print("\n🎓 步骤2: 训练教师模型")
    print("-" * 40)
    
    teacher_model_path = Path(config.TEACHER_OUTPUT_DIR) / "checkpoints" / "teacher_best.pt"
    
    if teacher_model_path.exists():
        print(f"发现已存在的教师模型: {teacher_model_path}")
        response = input("是否重新训练教师模型？(y/N): ")
        if response.lower() != 'y':
            print("跳过教师模型训练")
        else:
            print("开始训练教师模型...")
            teacher_model = train_teacher(config_class)
            if teacher_model is None:
                print("❌ 教师模型训练失败")
                return
    else:
        print("开始训练教师模型...")
        teacher_model = train_teacher(config_class)
        if teacher_model is None:
            print("❌ 教师模型训练失败")
            return
    
    print("✅ 教师模型训练完成")
    
    # 步骤3: 训练学生模型
    print("\n🎯 步骤3: 训练学生模型")
    print("-" * 40)
    
    student_model_path = Path(config.STUDENT_OUTPUT_DIR) / "checkpoints" / "student_best.pt"
    
    if student_model_path.exists():
        print(f"发现已存在的学生模型: {student_model_path}")
        response = input("是否重新训练学生模型？(y/N): ")
        if response.lower() != 'y':
            print("跳过学生模型训练")
        else:
            print("开始训练学生模型...")
            student_model = train_student(str(teacher_model_path), config_class)
            if student_model is None:
                print("❌ 学生模型训练失败")
                return
    else:
        print("开始训练学生模型...")
        student_model = train_student(str(teacher_model_path), config_class)
        if student_model is None:
            print("❌ 学生模型训练失败")
            return
    
    print("✅ 学生模型训练完成")
    
    # 训练完成总结
    print("\n🎉 训练流程完成！")
    print("=" * 60)
    print("📁 输出文件:")
    print(f"  教师模型: {teacher_model_path}")
    print(f"  学生模型: {student_model_path}")
    print(f"  教师训练日志: {config.TEACHER_OUTPUT_DIR}/logs")
    print(f"  学生训练日志: {config.STUDENT_OUTPUT_DIR}/logs")
    
    print("\n💡 后续操作:")
    print("  1. 查看训练日志:")
    print(f"     tensorboard --logdir {config.TEACHER_OUTPUT_DIR}/logs")
    print(f"     tensorboard --logdir {config.STUDENT_OUTPUT_DIR}/logs")
    print("  2. 测试翻译效果:")
    print(f"     python translate.py --teacher_model {teacher_model_path} --student_model {student_model_path} --text 'Guten Tag'")
    print("  3. 评估模型性能:")
    print("     python evaluate_models.py")


def run_teacher_only(config_class=Config):
    """只训练教师模型"""
    print("🎓 教师模型训练")
    
    # 检查数据
    config = config_class()
    if not Path(config.SPLIT_DATA_DIR).exists():
        print("分割数据集不存在，开始准备...")
        if not prepare_split_dataset():
            print("❌ 数据准备失败")
            return
    
    # 训练教师模型
    teacher_model = train_teacher(config_class)
    if teacher_model is not None:
        print("✅ 教师模型训练完成")
    else:
        print("❌ 教师模型训练失败")


def run_student_only(config_class=Config, teacher_model_path=None):
    """只训练学生模型"""
    print("🎯 学生模型训练")
    
    # 训练学生模型
    student_model = train_student(teacher_model_path, config_class)
    if student_model is not None:
        print("✅ 学生模型训练完成")
    else:
        print("❌ 学生模型训练失败")


def main():
    parser = argparse.ArgumentParser(description='德语-中文翻译模型训练系统')
    
    # 训练模式
    parser.add_argument('--mode', choices=['complete', 'teacher', 'student', 'data'], 
                       default='complete',
                       help='训练模式: complete=完整流程, teacher=只训练教师, student=只训练学生, data=只准备数据')
    
    # 配置类型
    parser.add_argument('--config', choices=['default', '4gb', 'quick'],
                       default='4gb',
                       help='配置类型: default=标准配置, 4gb=4GB显存优化, quick=快速测试')

    # 数据清洗选项
    parser.add_argument('--clean_data', action='store_true', default=True,
                       help='是否使用数据清洗')
    
    # 学生模型训练的教师模型路径
    parser.add_argument('--teacher_model', 
                       help='学生模型训练时使用的教师模型路径')
    
    # 其他选项
    parser.add_argument('--check_data', action='store_true',
                       help='检查数据准备状态')
    
    args = parser.parse_args()
    
    # 选择配置类
    config_classes = {
        'default': Config,
        '4gb': Config4GB,
        'quick': ConfigQuick
    }
    config_class = config_classes[args.config]
    
    # 执行相应的操作
    if args.check_data:
        check_data_status()
    elif args.mode == 'data':
        prepare_split_dataset(clean_data=args.clean_data)
    elif args.mode == 'complete':
        run_complete_training(config_class)
    elif args.mode == 'teacher':
        run_teacher_only(config_class)
    elif args.mode == 'student':
        run_student_only(config_class, args.teacher_model)
    else:
        parser.print_help()


def show_usage():
    """显示使用说明"""
    print("🔧 德语-中文翻译模型训练系统")
    print("\n📋 使用示例:")
    print("  # 完整训练流程（推荐）")
    print("  python main.py --mode complete --config default")
    print("\n  # 4GB显存优化训练")
    print("  python main.py --mode complete --config 4gb")
    print("\n  # 快速测试训练")
    print("  python main.py --mode complete --config quick")
    print("\n  # 只训练教师模型")
    print("  python main.py --mode teacher --config default")
    print("\n  # 只训练学生模型")
    print("  python main.py --mode student --teacher_model ./train_process/teacher/checkpoints/teacher_best.pt")
    print("\n  # 检查数据状态")
    print("  python main.py --check_data")
    print("\n  # 只准备数据")
    print("  python main.py --mode data")
    
    print("\n⚙️ 配置说明:")
    print("  default - 标准配置 (教师50轮，学生100轮)")
    print("  4gb     - 4GB显存优化 (教师20轮，学生40轮)")
    print("  quick   - 快速测试 (教师3轮，学生5轮)")
    
    print("\n📊 训练特性:")
    print("  ✓ 30000数据集分割")
    print("  ✓ 教师学生分离训练")
    print("  ✓ 梯度累积优化")
    print("  ✓ 混合精度训练")
    print("  ✓ 内存自动管理")
    print("  ✓ 训练进度监控")


if __name__ == "__main__":
    if len(sys.argv) == 1:
        show_usage()
    else:
        main()
